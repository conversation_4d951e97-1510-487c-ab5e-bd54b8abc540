import os
from typing import Any, Dict, List, Optional, Union
import aiohttp

__access_token: Optional[str] = os.environ.get('GOBOTS_TOKEN')


def extract_user_id_from_token(access_token: str) -> str:
    return access_token.split("-")[-1]


def get_access_token_from_gobots_data(user_id: str, data: List[Dict[str, Any]]) -> Optional[str]:
    for item in data:
        if item.get('user_id') == user_id:
            return item.get('access_token')
    return None


async def get_api_response(session: aiohttp.ClientSession) -> Optional[Union[Dict[str, Any], List[Any]]]:
    url = 'https://askhere.gobots.com.br/ml/all'
    headers = {'Authorization': f'Bearer {__access_token}'}

    async with session.get(url, headers=headers) as response:
        if response.status == 200:
            return await response.json()
        return None


def filter_merchants_data(go_bots_data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Filter go_bots_data based on provided criteria.

    Args:
        go_bots_data (list): List of merchant data from GoBots API
        filters (dict): Dictionary containing filter criteria:
            - merchant_ids (list, optional): List of merchant IDs to filter by
            - seller_ids (list, optional): List of seller IDs to filter by
            - merchant_id (str, optional): Single merchant ID to filter by
            - seller_id (str, optional): Single seller ID to filter by

    Returns:
        list: List of matching merchant data with structure:
            [{"merchant": merchant_id, "access_token": token, "seller_id": seller_id}, ...]
    """
    matches = []

    # Normalize inputs to lists
    merchant_ids = filters.get('merchant_ids', [])
    if filters.get('merchant_id'):
        merchant_ids = [filters['merchant_id']]

    seller_ids = filters.get('seller_ids', [])
    if filters.get('seller_id'):
        seller_ids = [filters['seller_id']]

    # Convert seller_ids to strings for comparison
    seller_ids = [str(sid) for sid in seller_ids] if seller_ids else []

    # If no merchant_ids provided, process all items
    if not merchant_ids:
        merchant_ids = [item.get('merchant', {}).get('id') for item in go_bots_data
                       if item.get('merchant') and item.get('merchant', {}).get('id')]

    for merchant_id in merchant_ids:
        # Find all items for this merchant
        merchant_matches = []
        for item in go_bots_data:
            if (item.get('merchant') and
                item.get('merchant', {}).get('id') == merchant_id and
                item.get('access_token')):

                seller_id = item['access_token'].split("-")[-1]
                merchant_match = {
                    "merchant": merchant_id,
                    "access_token": item['access_token'],
                    "seller_id": seller_id
                }

                # If seller_ids filter is provided, only include matching sellers
                if seller_ids:
                    if seller_id in seller_ids:
                        merchant_matches.append(merchant_match)
                else:
                    merchant_matches.append(merchant_match)

        matches.extend(merchant_matches)

    return matches
